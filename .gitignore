# Dependencies
node_modules/
pnpm-lock.yaml
yarn.lock
package-lock.json

# Build output
dist/
build/
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Operating System
.DS_Store
Thumbs.db
.directory

# Test coverage
coverage/
.nyc_output/

# TypeScript
*.tsbuildinfo

# Temporary files
*.swp
*.swo
.tmp/
temp/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache