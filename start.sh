#!/bin/bash

# 检查是否安装了所需的依赖
if ! command -v npm &> /dev/null; then
    echo "错误: 未安装 npm"
    exit 1
 fi

# 安装依赖
echo "正在安装项目依赖..."
cd backend && npm install
cd ../frontend && npm install
cd ..

# 安装concurrently
if ! npm list -g concurrently &> /dev/null; then
    echo "正在安装 concurrently..."
    npm install -g concurrently
fi

# 启动服务
echo "正在启动服务..."
concurrently \
    "cd backend && npm run dev" \
    "cd frontend && npm run dev"