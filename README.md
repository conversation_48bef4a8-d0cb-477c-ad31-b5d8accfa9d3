# JetBrains 私有插件仓库管理系统

## 项目概述

JetBrains 私有插件仓库管理系统是一个专为企业和开发团队设计的工具，用于管理和分发自定义的 JetBrains IDE 插件。该系统允许用户上传、管理和分发私有插件，同时提供版本控制、权限管理和插件兼容性检查等功能。

## 主要功能

- **插件管理**：上传、更新、删除和查看插件
- **版本控制**：管理插件的多个版本，支持版本回滚
- **用户认证**：基于JWT的用户认证系统
- **权限管理**：管理员可以创建用户和分配权限
- **插件兼容性**：检查和管理插件与不同IDE版本的兼容性
- **插件仓库XML**：自动生成符合JetBrains规范的插件仓库XML文件

## 技术栈

### 后端

- **Node.js** 和 **Express**：构建RESTful API
- **TypeScript**：提供类型安全
- **MinIO**：对象存储服务，用于存储插件文件
- **JWT**：用户认证
- **XML2JS**：XML解析和生成
- **AdmZip**：插件文件解析

### 前端

- **React**：用户界面构建
- **TypeScript**：类型安全
- **Ant Design**：UI组件库
- **React Query**：数据获取和缓存
- **React Router**：路由管理

## 系统架构

系统由以下主要组件组成：

1. **前端应用**：基于React的单页应用，提供用户界面
2. **后端API**：基于Express的RESTful API，处理业务逻辑
3. **MinIO存储**：存储插件文件和XML版本历史
4. **用户认证系统**：基于JWT的认证机制

## 安装与部署

### 环境要求

- Node.js 14+
- MinIO 服务器
- Docker 和 Docker Compose（可选，用于容器化部署）

### 使用Docker Compose部署

1. 克隆仓库：
   ```bash
   git clone ssh://***********************:2222/weibo_rd/common/wecode/jetbrains-custom-repository.git
   cd jetbrains-custom-repository
   ```

2. 配置环境变量：
   复制`.env.test`文件为`.env`并根据需要修改配置：
   ```bash
   cp .env.test .env
   ```

3. 启动服务：
   ```bash
   docker-compose up -d
   ```

4. 访问系统：
   打开浏览器访问 `http://localhost:3000`

### 手动部署

1. 克隆仓库：
   ```bash
   git clone ssh://***********************:2222/weibo_rd/common/wecode/jetbrains-custom-repository.git
   cd jetbrains-custom-repository
   ```

2. 安装依赖：
   ```bash
   # 安装后端依赖
   cd backend
   npm install
   
   # 安装前端依赖
   cd ../frontend
   npm install
   ```

3. 配置环境变量：
   复制`.env.test`文件为`.env`并根据需要修改配置

4. 构建和启动：
   ```bash
   # 构建后端
   cd backend
   npm run build
   
   # 构建前端
   cd ../frontend
   npm run build
   
   # 启动服务
   cd ../
   npm start
   ```

## 使用指南

### 管理员功能

1. **用户管理**：
   - 创建新用户
   - 分配用户权限
   - 删除用户

2. **插件管理**：
   - 创建新插件
   - 上传插件版本
   - 删除插件版本
   - 查看插件列表和详情

3. **版本控制**：
   - 查看版本历史
   - 回滚到指定版本

### 普通用户功能

1. **插件管理**：
   - 上传插件版本
   - 查看插件列表和详情

### 在JetBrains IDE中使用私有插件仓库

1. 打开IDE设置（File > Settings）
2. 导航到Plugins > 设置图标 > Manage Plugin Repositories
3. 添加仓库URL：`http://your-server:3000/plugins.xml`
4. 点击OK，现在可以从私有仓库安装插件了

## API文档

### 插件相关API

- `GET /api/plugins`：获取插件列表
- `POST /api/plugins`：创建新插件
- `GET /api/plugins/:pluginId`：获取插件详情
- `POST /api/plugins/:pluginId/versions`：上传新版本
- `DELETE /api/plugins/:pluginId/versions/:version`：删除插件版本
- `GET /api/plugins/versions`：获取版本历史
- `POST /api/plugins/versions/rollback`：回滚到指定版本

### 用户相关API

- `POST /api/users/login`：用户登录
- `GET /api/users`：获取用户列表（管理员）
- `POST /api/users`：创建新用户（管理员）
- `DELETE /api/users/:userId`：删除用户（管理员）
- `GET /api/users/permissions`：获取权限列表（管理员）
- `POST /api/users/permissions`：创建新权限（管理员）
- `DELETE /api/users/permissions/:permissionId`：删除权限（管理员）
- `GET /api/users/me`：获取当前用户信息
- `PUT /api/users/:userId/permissions`：更新用户权限（管理员）

## 贡献指南

1. Fork 仓库
2. 创建功能分支：`git checkout -b feature/your-feature-name`
3. 提交更改：`git commit -m 'Add some feature'`
4. 推送到分支：`git push origin feature/your-feature-name`
5. 提交Pull Request

## 许可证

[MIT License](LICENSE)

## 联系方式

如有问题或建议，请提交Issue或联系项目维护者。