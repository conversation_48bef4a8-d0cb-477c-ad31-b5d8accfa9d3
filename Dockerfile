# Build stage for Frontend
FROM node:20-alpine as frontend-builder
WORKDIR /app/frontend

# Install dependencies
COPY frontend/package.json frontend/package-lock.json ./
RUN npm ci

# Copy source code and build
COPY frontend/ ./
RUN npm run build && ls -la build/

# Build stage for Backend
FROM node:20-alpine as backend-builder
WORKDIR /app/backend
COPY backend/package.json ./
RUN npm install
COPY backend/ ./
RUN npm run build

# Production stage
FROM node:20-alpine
WORKDIR /app

# Copy backend build
COPY --from=backend-builder /app/backend/dist ./dist
COPY --from=backend-builder /app/backend/package.json ./

# Install production dependencies only
RUN npm install

# Copy frontend build to backend's public directory
COPY --from=frontend-builder /app/frontend/build ./public

ENV NODE_ENV=production
ENV MINIO_ENDPOINT=minharb.eos.grid.sina.com.cn
ENV MINIO_PORT=9100
ENV MINIO_USE_SSL=false
ENV MINIO_REGION=cn-north-1
ENV MINIO_ACCESS_KEY=U1jPA0LYOFGyadQXWLrr
ENV MINIO_SECRET_KEY=PAnUSZUMtm5HNi2DARd1Zu1u923pstR1KHIyrnEg
ENV MINIO_BUCKET=wecodeplugin
ENV MINIO_PUBLIC_URL=http://minharb.eos.grid.sina.com.cn:9100

# Expose the port the app runs on
EXPOSE 3000

# Start the application
CMD ["node", "dist/index.js"]