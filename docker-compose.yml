version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MINIO_ENDPOINT=minharb.eos.grid.sina.com.cn
      - MINIO_PORT=9100
      - MINIO_USE_SSL=false
      - MINIO_REGION=cn-north-1
      - MINIO_ACCESS_KEY=U1jPA0LYOFGyadQXWLrr
      - MINIO_SECRET_KEY=PAnUSZUMtm5HNi2DARd1Zu1u923pstR1KHIyrnEg
      - MINIO_BUCKET=wecodeplugin-test
      - MINIO_PUBLIC_URL=http://minharb.eos.grid.sina.com.cn:9100
    # volumes:
    #   - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s