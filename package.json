{"name": "jetbrains-custom-repository", "version": "1.0.0", "description": "JetBrains 私有插件仓库管理系统", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest"}, "dependencies": {"@aws-sdk/client-s3": "^3.0.0", "@types/adm-zip": "^0.5.7", "@types/minio": "^7.1.1", "adm-zip": "^0.5.16", "dotenv": "^16.5.0", "express": "^4.18.2", "minio": "^7.1.4", "multer": "^1.4.5-lts.1", "xml2js": "^0.6.2", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/xml2js": "^0.4.14", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}