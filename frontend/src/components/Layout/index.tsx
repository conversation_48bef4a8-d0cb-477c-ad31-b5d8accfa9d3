import React from 'react';
import { Layout as AntLayout, <PERSON><PERSON>, But<PERSON>, message } from 'antd';
import { Link, Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  AppstoreOutlined,
  UploadOutlined,
  SettingOutlined,
  UserOutlined,
  SafetyCertificateOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { logout } from '../../services/auth';
import { UserRole } from '../../types/user';

const { Header, Content, Sider } = AntLayout;

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, setUser } = useAuth();

  const handleLogout = () => {
    logout();
    setUser(null);
    message.success('已退出登录');
    navigate('/login');
  };

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">首页</Link>,
    },
    {
      key: '/plugins',
      icon: <AppstoreOutlined />,
      label: <Link to="/plugins">插件列表</Link>,
    },
  ];

  // 仅管理员可见的菜单项
  if (user?.role === UserRole.ADMIN) {
    menuItems.push(
      {
        key: '/plugins/create',
        icon: <AppstoreOutlined />,
        label: <Link to="/plugins/create">创建插件</Link>,
      },
      {
        key: '/upload',
        icon: <UploadOutlined />,
        label: <Link to="/upload">上传插件</Link>,
      },
      {
        key: '/settings',
        icon: <SettingOutlined />,
        label: <Link to="/settings">系统设置</Link>,
      },
      {
        key: '/users',
        icon: <UserOutlined />,
        label: <Link to="/users">用户管理</Link>,
      },
      {
        key: '/permissions',
        icon: <SafetyCertificateOutlined />,
        label: <Link to="/permissions">权限管理</Link>,
      }
    );
  }

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        padding: '0 24px', 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <div style={{ 
          color: '#1890ff', 
          fontSize: '18px', 
          fontWeight: 'bold' 
        }}>
          JetBrains 插件仓库
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 16 }}>
            {user?.username} ({user?.role === UserRole.ADMIN ? '管理员' : '用户'})
          </span>
          <Button 
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            退出登录
          </Button>
        </div>
      </Header>
      <AntLayout>
        <Sider width={200} style={{ background: '#fff' }}>
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            style={{ height: '100%', borderRight: 0 }}
            items={menuItems}
          />
        </Sider>
        <Content style={{ 
          padding: 24,
          margin: 0,
          minHeight: 280,
          background: '#fff'
        }}>
          <Outlet />
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;