import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  message,
  Select,
  Space,
} from 'antd';
import { createPlugin } from '../../services/api';

interface CreatePluginForm {
  name: string;
  id: string;
  category: string;
  vendor: string;
  vendorUrl: string;
  version: string;
  sinceBuild: string;
  untilBuild?: string;
  description: string;
}

export default function CreatePlugin() {
  const [form] = Form.useForm<CreatePluginForm>();
  const [submitting, setSubmitting] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (values: CreatePluginForm) => {
    try {
      setSubmitting(true);
      await createPlugin(values);

      message.success('插件创建成功');
      navigate('/upload');
    } catch (error) {
      message.error('创建失败：' + (error as Error).message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleNameChange = (value: string) => {
    // 自动生成插件ID
    const id = value.toLowerCase().replace(/\s+/g, '-');
    form.setFieldsValue({ id });
  };

  return (
    <Card title="创建新插件">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ maxWidth: 600 }}
      >
        <Form.Item
          label="插件名称"
          name="name"
          rules={[{ required: true, message: '请输入插件名称' }]}
        >
          <Input
            placeholder="请输入插件名称"
            onChange={(e) => handleNameChange(e.target.value)}
          />
        </Form.Item>

        <Form.Item
          label="插件ID"
          name="id"
          rules={[
            { required: true, message: '请输入插件ID' },
            {
              message: '插件ID',
            },
          ]}
        >
          <Input placeholder="plugin-id" />
        </Form.Item>

        <Form.Item
          label="分类"
          name="category"
          rules={[{ required: true, message: '请选择分类' }]}
        >
          <Select placeholder="请选择分类">
            <Select.Option value="Development Tools">开发工具</Select.Option>
            <Select.Option value="Version Controls">版本控制</Select.Option>
            <Select.Option value="Build Tools">构建工具</Select.Option>
            <Select.Option value="Languages">编程语言</Select.Option>
            <Select.Option value="Frameworks">框架</Select.Option>
            <Select.Option value="UI Tools">UI 工具</Select.Option>
            <Select.Option value="Other">其他</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="开发者"
          name="vendor"
          rules={[{ required: true, message: '请输入开发者名称' }]}
        >
          <Input placeholder="请输入开发者名称" />
        </Form.Item>

        <Form.Item
          label="开发者网站"
          name="vendorUrl"
          rules={[{ required: true, message: '请输入开发者网站' }]}
        >
          <Input placeholder="请输入开发者网站" />
        </Form.Item>

        <Form.Item
          label="版本号"
          name="version"
          rules={[{ required: true, message: '请输入版本号' }]}
          initialValue="1.0.0"
        >
          <Input placeholder="请输入版本号，例如：1.0.0" />
        </Form.Item>

        <Space>
          <Form.Item
            label="最低兼容版本"
            name="sinceBuild"
            rules={[{ required: true, message: '请输入最低兼容版本' }]}
          >
            <Input placeholder="例如：231.8109.91" />
          </Form.Item>

          <Form.Item label="最高兼容版本" name="untilBuild">
            <Input placeholder="例如：233.*" />
          </Form.Item>
        </Space>

        <Form.Item
          label="描述"
          name="description"
          rules={[{ required: true, message: '请输入插件描述' }]}
        >
          <Input.TextArea
            rows={4}
            placeholder="请输入插件描述，支持 HTML 格式"
          />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={submitting}>
              创建插件
            </Button>
            <Button onClick={() => navigate('/')}>取消</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}