import { Card, Row, Col, Button, Typography, Space } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { api } from '../../services/api';
import { PlusOutlined, AppstoreOutlined, CloudUploadOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

export default function Home() {
  const navigate = useNavigate();

  const { data: pluginData } = useQuery<any>(
    'plugins',
    async () => {
      const response = await api.get('/plugins');
      return response;
    }
  );

  const totalPlugins = pluginData
    ? pluginData['plugin-repository'].category.reduce(
        (acc: number, cat: any) => acc + (cat['idea-plugin']?.length || 0),
        0
      )
    : 0;

  const categories = pluginData
    ? new Set(pluginData['plugin-repository'].category.map((cat: any) => cat.$.name))
    : new Set();

  return (
    <div className="p-8">
      <Title level={2} className="mb-8 text-center">
        JetBrains 插件仓库
      </Title>

      <Row gutter={[24, 24]} className="mb-8">
        <Col span={12}>
          <Card>
            <Title level={4}>
              <AppstoreOutlined className="mr-2" />
              插件总数
            </Title>
            <Paragraph className="text-2xl font-bold">{totalPlugins}</Paragraph>
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Title level={4}>
              <AppstoreOutlined className="mr-2" />
              分类总数
            </Title>
            <Paragraph className="text-2xl font-bold">{categories.size}</Paragraph>
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        <Col span={12}>
          <Card
            hoverable
            onClick={() => navigate('/plugins/create')}
            className="text-center"
          >
            <Space direction="vertical" size="large">
              <PlusOutlined style={{ fontSize: '2rem' }} />
              <Title level={4}>创建新插件</Title>
              <Paragraph>创建一个新的JetBrains插件</Paragraph>
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card
            hoverable
            onClick={() => navigate('/upload')}
            className="text-center"
          >
            <Space direction="vertical" size="large">
              <CloudUploadOutlined style={{ fontSize: '2rem' }} />
              <Title level={4}>上传新版本</Title>
              <Paragraph>为现有插件上传新版本</Paragraph>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}