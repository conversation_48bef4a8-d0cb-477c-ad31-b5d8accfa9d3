import React from 'react';
import { Form, Input, But<PERSON>, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { login } from '../../services/auth';
import { LoginDto } from '../../types/user';
import styles from './styles.module.css';
import { useAuth } from '../../contexts/AuthContext';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { setUser } = useAuth();

  const handleSubmit = async (values: LoginDto) => {
    try {
      console.log('Login form values:', values);
      const data = await login(values);
      console.log('Login response:', data);
      setUser(data.user);
      message.success('登录成功');
      navigate('/');
    } catch (error) {
      console.error('Login error:', error);
      message.error('登录失败：' + (error as Error).message);
    }
  };

  return (
    <div className={styles.container}>
      <Card title="登录" className={styles.loginCard}>
        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block size="large">
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login;