import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import {
  Form,
  Input,
  Upload,
  Button,
  Card,
  Select,
  message,
  Typography
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile, RcFile } from 'antd/es/upload/interface';
import { uploadPlugin, api } from '../../services/api';

const { Title } = Typography;

interface UploadPluginForm {
  name: string;
  category: string;
  vendor: string;
  vendorUrl: string;
  version: string;
  sinceBuild: string;
  untilBuild?: string;
  description: string;
  changeNotes: string;
  plugin: UploadFile[];
}

interface PluginRepository {
  'plugin-repository': {
    category: Array<{
      $: { name: string };
      'idea-plugin': Plugin[];
    }>;
  };
}

export default function UploadPlugin() {
  const [form] = Form.useForm<UploadPluginForm>();
  const [uploading, setUploading] = useState(false);
  const [isNewPlugin, setIsNewPlugin] = useState(false);
  const [selectedPluginId, setSelectedPluginId] = useState<string | undefined>(undefined);
  const navigate = useNavigate();

  const { data: allPluginsData, isLoading: isLoadingPlugins } = useQuery<PluginRepository>(
    'allPlugins',
    async () => {
      const response = await api.get('/plugins') as PluginRepository;
      console.log("response: ", response);
      return response;
    }
  );

  const compareVersions = (v1: string, v2: string): number => {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const p1 = parts1[i] || 0;
      const p2 = parts2[i] || 0;
      if (p1 > p2) return 1;
      if (p1 < p2) return -1;
    }
    return 0;
  };

  const getNextVersion = (currentVersion: string): string => {
    const parts = currentVersion.split('.');
    const lastPart = parseInt(parts[parts.length - 1], 10);
    if (!isNaN(lastPart)) {
      parts[parts.length - 1] = (lastPart + 1).toString();
    }
    return parts.join('.');
  };

  const handlePluginNameChange = (value: string) => {
    if (value === '__NEW_PLUGIN__') {
      message.error('不支持创建新插件，请选择已有插件');
      return;
    }
    setIsNewPlugin(false);
    setSelectedPluginId(value);

    // 查找选中的插件的所有版本
    const selectedPluginVersions = allPluginsData?.['plugin-repository'].category
      .flatMap(cat => cat['idea-plugin'] || [])
      .filter(plugin => plugin.id[0] === value)
      .flatMap(plugin => plugin.version);

    // 获取所有版本中的最新版本
    const latestVersion = selectedPluginVersions
      ?.sort((a, b) => compareVersions(b, a))[0];

    // 查找选中的插件完整信息
    const selectedPlugin = allPluginsData?.['plugin-repository'].category
      .flatMap(cat => cat['idea-plugin'] || [])
      .find(plugin => plugin.id[0] === value);

    if (selectedPlugin && latestVersion) {
      // 设置表单值
      form.setFieldsValue({
        name: selectedPlugin.name[0],
        vendor: selectedPlugin.vendor[0]._,
        vendorUrl: selectedPlugin.vendor[0].$.url,
        version: getNextVersion(latestVersion),
        sinceBuild: selectedPlugin['idea-version'][0].$['since-build'],
        untilBuild: selectedPlugin['idea-version'][0].$['until-build'],
        description: selectedPlugin.description?.[0] || '',
        changeNotes: ''
      });
    }
  };

  const handleSubmit = async (values: UploadPluginForm) => {
    if (!values.plugin?.[0]) {
      message.error('请选择插件文件');
      return;
    }

    if (!selectedPluginId) {
      message.error('请选择插件');
      return;
    }

    // 检查版本号格式
    if (!/^\d+(\.\d+)*$/.test(values.version)) {
      message.error('版本号格式不正确，应为数字和点号组成，如 1.0.0');
      return;
    }

    // 检查版本号是否大于当前最新版本
    const selectedPluginVersions = allPluginsData?.['plugin-repository'].category
      .flatMap(cat => cat['idea-plugin'] || [])
      .filter(plugin => plugin.id[0] === selectedPluginId)
      .flatMap(plugin => plugin.version);

    if (selectedPluginVersions?.length) {
      const latestVersion = selectedPluginVersions
        .sort((a, b) => compareVersions(b, a))[0];

      // if (compareVersions(values.version, latestVersion) <= 0) {
      //   message.error(`新版本号必须大于当前最新版本 ${latestVersion}`);
      //   return;
      // }

      // // 检查是否已存在相同版本
      // if (selectedPluginVersions.includes(values.version)) {
      //   message.error('该版本号已存在');
      //   return;
      // }
    }

    const formData = new FormData();
    formData.append('plugin', values.plugin[0].originFileObj as RcFile);
    formData.append('name', values.name);
    formData.append('category', values.category);
    formData.append('vendor', values.vendor);
    formData.append('vendorUrl', values.vendorUrl);
    formData.append('version', values.version);
    formData.append('sinceBuild', values.sinceBuild);
    if (values.untilBuild) {
      formData.append('untilBuild', values.untilBuild);
    }
    formData.append('description', values.description);
    formData.append('changeNotes', values.changeNotes);

    setUploading(true);
    try {
      await uploadPlugin(selectedPluginId, formData);
      message.success('插件上传成功');
      navigate('/plugins');
    } catch (error) {
      console.error('Error uploading plugin:', error);
      message.error('插件上传失败');
    } finally {
      setUploading(false);
    }
  };

  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };

  if (isLoadingPlugins) {
    return <div>Loading...</div>;
  }

  const plugins = allPluginsData?.['plugin-repository'].category
    .flatMap(cat => cat['idea-plugin'] || [])
    .reduce((unique, plugin) => {
      const existingPlugin = unique.find(p => p.id[0] === plugin.id[0]);
      if (!existingPlugin) {
        unique.push(plugin);
      }
      return unique;
    }, [] as Plugin[])
    .sort((a, b) => a.name[0].localeCompare(b.name[0]));

  return (
    <Card>
      <Title level={2}>上传插件</Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          category: 'Development Tools'
        }}
      >
        <Form.Item
          label="选择插件"
          name="pluginId"
          rules={[{ required: true, message: '请选择插件' }]}
        >
          <Select
            placeholder="选择要更新的插件"
            onChange={handlePluginNameChange}
            loading={isLoadingPlugins}
          >
            {plugins?.map(plugin => (
              <Select.Option key={plugin.id[0]} value={plugin.id[0]}>
                {plugin.name[0]}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="插件名称"
          name="name"
          rules={[{ required: true, message: '请输入插件名称' }]}
        >
          <Input disabled={!isNewPlugin} />
        </Form.Item>

        <Form.Item
          label="分类"
          name="category"
          rules={[{ required: true, message: '请选择分类' }]}
        >
          <Select>
            <Select.Option value="Development Tools">Development Tools</Select.Option>
            <Select.Option value="Build Tools">Build Tools</Select.Option>
            <Select.Option value="Version Control">Version Control</Select.Option>
            <Select.Option value="Languages">Languages</Select.Option>
            <Select.Option value="Frameworks">Frameworks</Select.Option>
            <Select.Option value="UI Designer">UI Designer</Select.Option>
            <Select.Option value="Editor">Editor</Select.Option>
            <Select.Option value="Keymap">Keymap</Select.Option>
            <Select.Option value="Theme">Theme</Select.Option>
            <Select.Option value="Other Tools">Other Tools</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="开发者"
          name="vendor"
          rules={[{ required: true, message: '请输入开发者名称' }]}
        >
          <Input disabled={!isNewPlugin} />
        </Form.Item>

        <Form.Item
          label="开发者网站"
          name="vendorUrl"
          rules={[{ required: true, message: '请输入开发者网站' }]}
        >
          <Input disabled={!isNewPlugin} />
        </Form.Item>

        <Form.Item
          label="版本号"
          name="version"
          rules={[{ required: true, message: '请输入版本号' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="最低兼容版本"
          name="sinceBuild"
          rules={[{ required: true, message: '请输入最低兼容版本' }]}
        >
          <Input disabled={!isNewPlugin} />
        </Form.Item>

        <Form.Item
          label="最高兼容版本"
          name="untilBuild"
        >
          <Input disabled={!isNewPlugin} />
        </Form.Item>

        <Form.Item
          label="描述"
          name="description"
          rules={[{ required: true, message: '请输入插件描述' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item
          label="更新说明"
          name="changeNotes"
          rules={[{ required: true, message: '请输入更新说明' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item
          label="插件文件"
          name="plugin"
          valuePropName="fileList"
          getValueFromEvent={normFile}
          rules={[{ required: true, message: '请选择插件文件' }]}
        >
          <Upload
            maxCount={1}
            beforeUpload={() => false}
            accept=".jar,.zip"
          >
            <Button icon={<UploadOutlined />}>选择文件</Button>
          </Upload>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={uploading}>
            上传
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}

interface Plugin {
  id: string[];
  name: string[];
  version: string[];
  vendor: Array<{
    _: string;
    $: { url: string };
  }>;
  'idea-version': Array<{
    $: {
      'since-build': string;
      'until-build'?: string;
    };
  }>;
  description?: string[];
}