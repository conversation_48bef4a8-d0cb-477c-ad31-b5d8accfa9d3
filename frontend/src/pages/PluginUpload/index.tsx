import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Upload,
  message,
  Card,
  Select,
  Typography,
  Space
} from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import { uploadPlugin } from '@/services/api';

const { Dragger } = Upload;
const { Title } = Typography;

interface UploadFormValues {
  pluginId: string;
  name: string;
  category: string;
  vendor: string;
  vendorUrl: string;
  version: string;
  sinceBuild: string;
  untilBuild?: string;
  description: string;
  changeNotes: string;
}

const PluginUpload = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  const handleUpload = async (values: UploadFormValues) => {
    if (fileList.length === 0) {
      message.error('请选择要上传的插件文件');
      return;
    }

    const formData = new FormData();
    fileList[0].originFileObj && formData.append('plugin', fileList[0].originFileObj);
    
    Object.entries(values).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, value);
      }
    });

    setUploading(true);
    try {
      await uploadPlugin(values.pluginId, formData);
      message.success('插件上传成功');
      navigate('/');
    } catch (error) {
      message.error('上传失败：' + (error as Error).message);
    } finally {
      setUploading(false);
    }
  };

  const uploadProps = {
    onRemove: () => {
      setFileList([]);
    },
    beforeUpload: (file: UploadFile) => {
      if (!file.name.endsWith('.zip') && !file.name.endsWith('.jar')) {
        message.error('只支持上传 .zip 或 .jar 格式的插件文件');
        return false;
      }
      setFileList([file]);
      return false;
    },
    fileList,
  };

  return (
    <div>
      <div className="content-header">
        <Title level={2}>上传插件</Title>
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
          initialValues={{
            category: 'Development Tools'
          }}
        >
          <Form.Item
            label="插件 ID"
            name="pluginId"
            rules={[{ required: true, message: '请输入插件 ID' }]}
            tooltip="插件的唯一标识符，例如：com.example.plugin"
          >
            <Input placeholder="com.example.plugin" />
          </Form.Item>

          <Form.Item
            label="插件名称"
            name="name"
            rules={[{ required: true, message: '请输入插件名称' }]}
          >
            <Input placeholder="插件显示名称" />
          </Form.Item>

          <Form.Item
            label="分类"
            name="category"
            rules={[{ required: true, message: '请选择插件分类' }]}
          >
            <Select>
              <Select.Option value="Development Tools">开发工具</Select.Option>
              <Select.Option value="Code Tools">代码工具</Select.Option>
              <Select.Option value="Build Tools">构建工具</Select.Option>
              <Select.Option value="Version Controls">版本控制</Select.Option>
              <Select.Option value="Languages">编程语言</Select.Option>
              <Select.Option value="Frameworks">框架</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="开发者"
            name="vendor"
            rules={[{ required: true, message: '请输入开发者名称' }]}
          >
            <Input placeholder="开发者或组织名称" />
          </Form.Item>

          <Form.Item
            label="开发者网站"
            name="vendorUrl"
            rules={[{ required: true, message: '请输入开发者网站' }]}
          >
            <Input placeholder="https://example.com" />
          </Form.Item>

          <Form.Item
            label="版本号"
            name="version"
            rules={[{ required: true, message: '请输入版本号' }]}
          >
            <Input placeholder="1.0.0" />
          </Form.Item>

          <Space size="large">
            <Form.Item
              label="最低 IDE 版本"
              name="sinceBuild"
              rules={[{ required: true, message: '请输入最低支持的 IDE 版本' }]}
            >
              <Input placeholder="203" />
            </Form.Item>

            <Form.Item
              label="最高 IDE 版本"
              name="untilBuild"
            >
              <Input placeholder="233.*" />
            </Form.Item>
          </Space>

          <Form.Item
            label="插件描述"
            name="description"
            rules={[{ required: true, message: '请输入插件描述' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="支持 HTML 格式"
            />
          </Form.Item>

          <Form.Item
            label="更新日志"
            name="changeNotes"
            rules={[{ required: true, message: '请输入更新日志' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="支持 HTML 格式"
            />
          </Form.Item>

          <Form.Item
            label="插件文件"
            required
            tooltip="支持 .zip 或 .jar 格式的插件包"
          >
            <Dragger {...uploadProps} className="upload-dragger">
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持单个 .zip 或 .jar 格式的插件包
              </p>
            </Dragger>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={uploading}>
              上传插件
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default PluginUpload;