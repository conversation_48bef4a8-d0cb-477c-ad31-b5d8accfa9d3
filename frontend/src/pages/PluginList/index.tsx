import { useState } from 'react';
import { useQuery } from 'react-query';
import { Table, Card, Tag, Space, Button, Popconfirm, message, Modal, Form, Input } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { formatDate } from '../../utils/date';
import { api } from '../../services/api';

export interface Plugin {
  name: string[];
  id: string[];
  version: string[];
  vendor: Array<{
    _: string;
    $: { url: string };
  }>;
  'idea-version': Array<{
    $: {
      'since-build': string;
      'until-build'?: string;
    };
  }>;
  'download-url': string[];
  description: string[];
  'change-notes': string[];
  size?: string[];
  date?: string[];
  updatedDate?: string[];
}

interface Category {
  $: { name: string };
  'idea-plugin': Plugin[];
}

interface PluginRepository {
  'plugin-repository': {
    category: Category[];
  };
}

interface PluginVersion extends Plugin {
  key: string;
  parentId: string;
}

interface PluginGroup {
  key: string;
  name: string;
  id: string;
  vendor: {
    name: string;
    url: string;
  };
  children: PluginVersion[];
}

export default function PluginList() {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingPlugin, setEditingPlugin] = useState<Plugin | null>(null);
  const [form] = Form.useForm();

  const { data: pluginData, isLoading, refetch } = useQuery<PluginRepository>(
    'plugins',
    async () => {
      return await api.get('/plugins') as PluginRepository;
    }
  );

  const handleDelete = async (pluginId: string, version: string) => {
    try {
      await api.delete(`/plugins/${pluginId}/versions/${version}`);
      message.success('插件删除成功');
      refetch();
    } catch (error) {
      message.error('删除插件失败');
    }
  };

  const handleEdit = (plugin: Plugin) => {
    setEditingPlugin(plugin);
    form.setFieldsValue({
      name: plugin.name[0],
      vendor: plugin.vendor[0]._,
      vendorUrl: plugin.vendor[0].$.url,
      description: plugin.description[0],
      sinceBuild: plugin['idea-version'][0].$['since-build'],
      untilBuild: plugin['idea-version'][0].$['until-build'] || '',
    });
    setEditModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (!editingPlugin) return;

      await api.put(`/plugins/${editingPlugin.id[0]}/versions/${editingPlugin.version[0]}`, {
        name: values.name,
        vendor: {
          _: values.vendor,
          $: { url: values.vendorUrl }
        },
        description: [values.description],
        'idea-version': [{
          $: {
            'since-build': values.sinceBuild,
            'until-build': values.untilBuild || undefined
          }
        }]
      });

      message.success('插件更新成功');
      setEditModalVisible(false);
      refetch();
    } catch (error) {
      message.error('更新插件失败');
    }
  };

  const columns: ColumnsType<PluginGroup> = [
    {
      title: '名称',
      key: 'name',
      dataIndex: 'name',
      render: (_, plugin: PluginGroup) => (
        <Space direction="vertical" size={0}>
          <span className="font-semibold text-gray-800">{plugin.name}</span>
          <span className="text-gray-500 text-sm">{plugin.id}</span>
        </Space>
      ),
    },
    {
      title: '开发者',
      key: 'vendor',
      dataIndex: ['vendor', 'name'],
      render: (_, plugin: PluginGroup) => (
        <a href={plugin.vendor.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
          {plugin.vendor.name}
        </a>
      ),
    },
  ];

  const expandedColumns: ColumnsType<PluginVersion> = [
    {
      title: '版本',
      dataIndex: ['version', 0],
      key: 'version',
    },
    {
      title: '兼容版本',
      key: 'compatibility',
      render: (plugin: PluginVersion) => {
        const sinceBuild = plugin['idea-version'][0].$?.['since-build'];
        const untilBuild = plugin['idea-version'][0].$?.['until-build'];
        return (
          <Tag className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 ring-1 ring-inset ring-blue-200">
            {sinceBuild}
            {untilBuild ? ` - ${untilBuild}` : '+'}
          </Tag>
        );
      },
    },
    {
      title: '更新时间',
      key: 'date',
      render: (plugin: PluginVersion) => {
        const date = plugin.updatedDate?.[0] || plugin.date?.[0];
        return date ? formatDate(parseInt(date)) : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (plugin: PluginVersion) => (
        <Space>
          <Button
            type="link"
            href={plugin['download-url'][0]}
            target="_blank"
            className="text-blue-600 hover:text-blue-800 p-0"
          >
            下载
          </Button>
          <Button
            type="link"
            className="text-blue-600 hover:text-blue-800 p-0"
            onClick={() => handleEdit(plugin)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个版本吗？"
            onConfirm={() => handleDelete(plugin.id[0], plugin.version[0])}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger className="text-red-500 hover:text-red-700 p-0">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const categories = pluginData
    ? ['All', ...new Set(pluginData['plugin-repository'].category
        .map(cat => cat.$.name))]
    : ['All'];

  // 全局聚合 pluginMap
const pluginMap = new Map<string, PluginGroup>();

pluginData?.['plugin-repository'].category.forEach(cat => {
  cat['idea-plugin']?.forEach(plugin => {
    const pluginId = plugin.id[0];
    const versionObj: PluginVersion = {
      ...plugin,
      key: `${pluginId}-${plugin.version[0]}`,
      parentId: pluginId,
    };

    if (!pluginMap.has(pluginId)) {
      pluginMap.set(pluginId, {
        key: pluginId,
        name: plugin.name[0],
        id: pluginId,
        vendor: {
          name: plugin.vendor[0]._,
          url: plugin.vendor[0].$.url,
        },
        children: [versionObj],
      });
    } else {
      pluginMap.get(pluginId)!.children.push(versionObj);
    }
  });
});

// 过滤 category + 排序
const plugins = Array.from(pluginMap.values())
  .filter(group => {
    if (selectedCategory === 'All') return true;
    // 只有当 plugin 存在于 selectedCategory 的 category 里才展示
    const matchedCategory = pluginData?.['plugin-repository'].category.find(cat =>
      cat.$.name === selectedCategory &&
      cat['idea-plugin']?.some(plugin => plugin.id[0] === group.id)
    );
    return !!matchedCategory;
  })
  .map(group => ({
    ...group,
    children: group.children.sort((a, b) => {
      const dateA = a.updatedDate?.[0] || a.date?.[0] || '0';
      const dateB = b.updatedDate?.[0] || b.date?.[0] || '0';
      return parseInt(dateB) - parseInt(dateA);
    }),
  }))
  .sort((a, b) => a.name.localeCompare(b.name));

  return (
    <Card
      title={<span className="text-xl font-bold text-gray-800">插件列表</span>}
      extra={
        <Space>
          {categories.map(cat => (
            <Button
              key={cat}
              onClick={() => setSelectedCategory(cat)}
              className={`
                rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200
                ${selectedCategory === cat
                  ? 'bg-blue-600 text-white shadow-md hover:bg-blue-700'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }
              `}
            >
              {cat}
            </Button>
          ))}
        </Space>
      }
    >
      <Table
        columns={columns}
        expandable={{
          expandedRowRender: (record) => (
            <div className="pl-8 pr-4">
              <Table
                columns={expandedColumns}
                dataSource={record.children}
                pagination={false}
                showHeader={true}
                size="small"
                className="border border-gray-200 rounded-md"
              />
            </div>
          ),
          defaultExpandAllRows: false,
        }}
        dataSource={plugins}
        rowKey="key"
        loading={isLoading}
        pagination={false}
        size="middle"
        className="[&_.ant-table-cell]:!py-2"
      />

      <Modal
        title="编辑插件信息"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="插件名称"
            rules={[{ required: true, message: '请输入插件名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="vendor"
            label="开发者名称"
            rules={[{ required: true, message: '请输入开发者名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="vendorUrl"
            label="开发者网站"
            rules={[{ required: true, message: '请输入开发者网站' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="插件描述"
            rules={[{ required: true, message: '请输入插件描述' }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="sinceBuild"
            label="最低兼容版本"
            rules={[{ required: true, message: '请输入最低兼容版本' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="untilBuild"
            label="最高兼容版本"
          >
            <Input placeholder="留空表示无限制" />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
}