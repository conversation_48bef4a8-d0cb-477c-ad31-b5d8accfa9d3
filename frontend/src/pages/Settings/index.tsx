import React, { useState } from 'react';
import { Card, Form, InputNumber, Typography, Button, message, Table, Modal, Divider } from 'antd';
import { useQuery, useMutation } from 'react-query';
import { fetchSettings, updateSettings, getVersionHistory, rollbackToVersion } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;

interface SettingsFormValues {
  maxPluginSize: number;
  versionRetention: number;
}

interface VersionInfo {
  fileName: string;
  timestamp: string;
}

const Settings = () => {
  const [form] = Form.useForm<SettingsFormValues>();
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);

  const { data: settings, isLoading: isLoadingSettings } = useQuery('settings', fetchSettings, {
    onSuccess: (data: SettingsFormValues) => {
      form.setFieldsValue(data);
    }
  });

  const { mutate: saveSettings, isLoading: isSaving } = useMutation(
    (values: SettingsFormValues) => updateSettings(values),
    {
      onSuccess: () => {
        message.success('设置保存成功');
      },
      onError: (error: Error) => {
        message.error('保存失败：' + error.message);
      }
    }
  );

  const handleSubmit = (values: SettingsFormValues) => {
    saveSettings(values);
  };

  const { data: versions, isLoading: isLoadingVersions, refetch: refetchVersions } = useQuery(
    'versions',
    async () => {
      const response = await getVersionHistory();
      return { versions: response.data as unknown as VersionInfo[] };
    },
    {
      select: (data: { versions: VersionInfo[] }) => data.versions,
    }
  );

  const { mutate: handleRollback, isLoading: isRollingBack } = useMutation(
    (version: string) => rollbackToVersion(version),
    {
      onSuccess: () => {
        message.success('回滚成功');
        setSelectedVersion(null);
        refetchVersions();
      },
      onError: (error: Error) => {
        message.error('回滚失败：' + error.message);
      }
    }
  );

  const confirmRollback = () => {
    if (!selectedVersion) return;
    handleRollback(selectedVersion);
  };

  const columns: ColumnsType<VersionInfo> = [
    {
      title: '版本文件',
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: '创建时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          type="link" 
          onClick={() => setSelectedVersion(record.fileName)}
        >
          回滚到此版本
        </Button>
      ),
    },
  ];

  return (
    <div>
      <div className="content-header">
        <Title level={2}>系统设置</Title>
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={isLoadingSettings}
        >
          <Form.Item
            label="最大插件大小 (MB)"
            name="maxPluginSize"
            rules={[{ required: true, message: '请输入最大插件大小' }]}
            tooltip="单个插件包允许的最大大小，单位：MB"
          >
            <InputNumber
              min={1}
              max={1000}
              style={{ width: '100%' }}
              placeholder="请输入最大插件大小"
            />
          </Form.Item>

          <Form.Item
            label="版本保留数量"
            name="versionRetention"
            rules={[{ required: true, message: '请输入版本保留数量' }]}
            tooltip="每个插件最多保留的历史版本数量"
          >
            <InputNumber
              min={1}
              max={100}
              style={{ width: '100%' }}
              placeholder="请输入版本保留数量"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={isSaving}>
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Divider />

      <Card title="版本管理">
        <Table
          columns={columns}
          dataSource={versions}
          rowKey="fileName"
          loading={isLoadingVersions}
        />
      </Card>

      <Modal
        title="确认回滚"
        open={!!selectedVersion}
        onOk={confirmRollback}
        onCancel={() => setSelectedVersion(null)}
        confirmLoading={isRollingBack}
        okText="确认"
        cancelText="取消"
      >
        <p>确定要回滚到版本 {selectedVersion} 吗？此操作不可撤销。</p>
      </Modal>
    </div>
  );
};

export default Settings;