import React, { useState } from 'react';
import { Table, Button, Modal, Form, Input, Space, message, Popconfirm, Select, Tabs } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { getPermissions, createPermission, deletePermission, getUsers, updateUserPermissions } from '../../services/auth';
import { Permission, CreatePermissionDto, User } from '../../types/user';

const { Option } = Select;
const { TabPane } = Tabs;

const PermissionManagement: React.FC = () => {
  const [form] = Form.useForm();
  const [permissionForm] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isUserPermissionModalVisible, setIsUserPermissionModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const queryClient = useQueryClient();

  const { data: permissions, isLoading: permissionsLoading } = useQuery<Permission[]>('permissions', getPermissions);
  const { data: users, isLoading: usersLoading } = useQuery<User[]>('users', getUsers);

  const createPermissionMutation = useMutation(createPermission, {
    onSuccess: () => {
      queryClient.invalidateQueries('permissions');
      message.success('权限创建成功');
      setIsModalVisible(false);
      form.resetFields();
    },
    onError: (error: Error) => {
      message.error('创建失败：' + error.message);
    },
  });

  const deletePermissionMutation = useMutation(deletePermission, {
    onSuccess: () => {
      queryClient.invalidateQueries('permissions');
      message.success('权限删除成功');
    },
    onError: (error: Error) => {
      message.error('删除失败：' + error.message);
    },
  });

  const handleCreate = async (values: CreatePermissionDto) => {
    createPermissionMutation.mutate(values);
  };

  const handleDelete = (permissionId: string) => {
    deletePermissionMutation.mutate(permissionId);
  };

  const columns = [
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '路径',
      dataIndex: 'path',
      key: 'path',
    },
    {
      title: '请求方法',
      dataIndex: 'method',
      key: 'method',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: Permission) => (
        <Space>
          <Popconfirm
            title="确定要删除这个权限吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const updateUserPermissionsMutation = useMutation(
    (data: { userId: string; permissions: string[] }) => updateUserPermissions(data.userId, data.permissions),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('users');
        message.success('用户权限更新成功');
        setIsUserPermissionModalVisible(false);
      },
      onError: (error: Error) => {
        message.error('更新失败：' + error.message);
      },
    }
  );

  const handleUpdateUserPermissions = (values: { permissions: string[] }) => {
    if (selectedUser) {
      updateUserPermissionsMutation.mutate({
        userId: selectedUser.id,
        permissions: values.permissions,
      });
    }
  };

  const handleEditUserPermissions = (user: User) => {
    setSelectedUser(user);
    permissionForm.setFieldsValue({
      permissions: user.permissions || [],
    });
    setIsUserPermissionModalVisible(true);
  };

  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: '权限数量',
      key: 'permissionsCount',
      render: (_: unknown, record: User) => record.permissions?.length || 0,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: User) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => handleEditUserPermissions(record)}
          >
            编辑权限
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Tabs defaultActiveKey="users">
        <TabPane tab="用户权限管理" key="users">
          <Table
            columns={userColumns}
            dataSource={users}
            rowKey="id"
            loading={usersLoading}
          />
        </TabPane>
        <TabPane tab="权限列表" key="permissions">
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          创建权限
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={permissions}
        rowKey="id"
        loading={permissionsLoading}
      />

      <Modal
        title="创建新权限"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={createPermissionMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
        >
          <Form.Item
            name="name"
            label="权限名称"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入权限描述' }]}
          >
            <Input.TextArea />
          </Form.Item>

          <Form.Item
            name="path"
            label="路径"
            rules={[{ required: true, message: '请输入路径' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="method"
            label="请求方法"
            rules={[{ required: true, message: '请输入请求方法' }]}
          >
            <Input placeholder="GET, POST, PUT, DELETE" />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={`编辑用户权限 - ${selectedUser?.username}`}
        open={isUserPermissionModalVisible}
        onCancel={() => {
          setIsUserPermissionModalVisible(false);
          permissionForm.resetFields();
          setSelectedUser(null);
        }}
        onOk={() => permissionForm.submit()}
        confirmLoading={updateUserPermissionsMutation.isLoading}
      >
        <Form
          form={permissionForm}
          layout="vertical"
          onFinish={handleUpdateUserPermissions}
        >
          <Form.Item
            name="permissions"
            label="选择权限"
            rules={[{ required: true, message: '请选择至少一个权限' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择权限"
              style={{ width: '100%' }}
            >
              {permissions?.map((permission: Permission) => (
                <Option key={permission.id} value={permission.id}>
                  {permission.name} - {permission.description}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PermissionManagement;