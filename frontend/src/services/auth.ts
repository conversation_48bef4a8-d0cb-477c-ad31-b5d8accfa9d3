import { api } from './api';
import { User, CreateUserDto, LoginDto, Permission, CreatePermissionDto } from '../types/user';

export const login = async (loginDto: LoginDto) => {
  console.log('Sending login request:', loginDto);
  const response = await api.post(`/users/login`, loginDto) as {user: User, token: string};
  console.log('Login raw response:', response);
  if (!response || !response.token) {
    console.error('Invalid login response:', response);
    throw new Error('登录响应无效');
  }
  localStorage.setItem('token', response.token);
  return response;
};

export const logout = () => {
  localStorage.removeItem('token');
};

export const getCurrentUser = async (): Promise<User | null> => {
  const token = localStorage.getItem('token');
  if (!token) return null;

  try {
    const { data } = await api.get(`/users/me`);
    return data;
  } catch (error) {
    logout();
    return null;
  }
};

export const getUsers = async (): Promise<User[]> => {
  const { data } = await api.get(`/users`);
  return data;
};

export const createUser = async (createUserDto: CreateUserDto): Promise<User> => {
  const { data } = await api.post(`/users`, createUserDto);
  return data;
};

export const deleteUser = async (userId: string) => {
  await api.delete(`/users/${userId}`);
};

export const getPermissions = async (): Promise<Permission[]> => {
  const { data } = await api.get(`/users/permissions`);
  return data;
};

export const createPermission = async (createPermissionDto: CreatePermissionDto): Promise<Permission> => {
  const { data } = await api.post(`/users/permissions`, createPermissionDto);
  return data;
};

export const deletePermission = async (permissionId: string) => {
  await api.delete(`/users/permissions/${permissionId}`);
};

export const updateUserPermissions = async (userId: string, permissions: string[]) => {
  const response = await api.put(`/users/${userId}/permissions`, { permissions });
  return response;
};