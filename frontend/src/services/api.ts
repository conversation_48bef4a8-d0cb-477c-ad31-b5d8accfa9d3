import axios from 'axios';
import { logout } from './auth';

export const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
});

api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response?.status === 401) {
      logout();
      window.location.href = '/login';
    }
    if (error.response) {
      throw new Error(error.response.data.message || '请求失败');
    }
    throw error;
  }
);

export const fetchPlugins = () => api.get('/plugins');

export const uploadPlugin = (pluginId: string, formData: FormData) =>
  api.post(`/plugins/${pluginId}/versions`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

export const deletePluginVersion = (pluginId: string, version: string) =>
  api.delete(`/plugins/${pluginId}/versions/${version}`);

export const fetchSettings = () => api.get('/settings');

export const updateSettings = (data: Settings) => api.put('/settings', data);

export const getVersionHistory = () => api.get('/plugins/versions');

export const rollbackToVersion = (version: string) => api.post('/plugins/versions/rollback', { version });

export const createPlugin = (data: any) => api.post('/plugins', data);

export interface Settings {
  maxPluginSize: number;
  versionRetention: number;
}

export interface VersionInfo {
  fileName: string;
  timestamp: string;
}