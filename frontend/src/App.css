@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>,
    'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

body {
  margin: 0;
  padding: 0;
  background-color: #f8fafc;
}

.ant-layout-header {
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.ant-layout-content {
  min-height: calc(100vh - 64px);
  background-color: #f8fafc;
  padding: 24px;
}

.ant-menu {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  border-bottom: none !important;
}

.ant-menu-item {
  margin: 0 8px !important;
}

.ant-menu-item a {
  color: rgba(0, 0, 0, 0.88) !important;
}

.ant-menu-item-selected a {
  color: #1677ff !important;
}

.ant-card {
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  transform: translateY(-1px);
}

.ant-table-wrapper .ant-table-thead > tr > th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #1e293b;
}

.ant-table-wrapper .ant-table-tbody > tr:hover > td {
  background-color: #f1f5f9;
}

.ant-form-item-label > label {
  font-weight: 500;
  color: #1e293b;
}

.ant-btn {
  transition: all 0.2s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
}

.ant-input, .ant-select-selector {
  border-radius: 6px !important;
  transition: all 0.2s ease;
}

.ant-input:focus, .ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}