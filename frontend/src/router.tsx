import { createBrowserRouter } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import PluginList from './pages/PluginList';
import UploadPlugin from './pages/UploadPlugin';
import CreatePlugin from './pages/CreatePlugin';
import Settings from './pages/Settings';
import Login from './pages/Login';
import UserManagement from './pages/UserManagement';
import PermissionManagement from './pages/PermissionManagement';
import ProtectedRoute from './components/ProtectedRoute';

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <Layout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: 'plugins',
        element: <PluginList />,
      },
      {
        path: 'plugins/create',
        element: (
          <ProtectedRoute requireAdmin>
            <CreatePlugin />
          </ProtectedRoute>
        ),
      },
      {
        path: 'upload',
        element: (
          <ProtectedRoute requireAdmin>
            <UploadPlugin />
          </ProtectedRoute>
        ),
      },
      {
        path: 'settings',
        element: (
          <ProtectedRoute requireAdmin>
            <Settings />
          </ProtectedRoute>
        ),
      },
      {
        path: 'users',
        element: (
          <ProtectedRoute requireAdmin>
            <UserManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: 'permissions',
        element: (
          <ProtectedRoute requireAdmin>
            <PermissionManagement />
          </ProtectedRoute>
        ),
      },
    ],
  },
]);