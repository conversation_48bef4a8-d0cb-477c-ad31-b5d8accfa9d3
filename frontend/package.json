{"name": "jetbrains-plugin-repo-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.0.0", "@ant-design/pro-components": "^2.6.49", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "antd": "^5.13.2", "axios": "^1.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.21.3"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^20.10.0", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "typescript": "^4.9.5", "vite": "^5.0.11", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.1"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview", "test": "jest"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}