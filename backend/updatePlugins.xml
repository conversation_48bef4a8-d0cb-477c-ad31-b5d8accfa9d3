<?xml version="1.0" encoding="UTF-8"?>
<plugin-repository>
  <category name="Keymap">
    <idea-plugin size="36235887" date="1726031283000" updatedDate="1726031283000" url="http://wecode.api.weibo.com/idea/download/updatePlugins.xml">
      <name>Wecode</name>
      <id>com.weibo.assistant</id>
      <version>3.0.17</version>
      <vendor url="https://weibo.com">Wecode</vendor>
      <idea-version since-build="232"/>
      <download-url>http://wecode.api.weibo.com/idea/download/plugins/com.weibo.assistant/CodeAssistant/3.0.17/CodeAssistant-3.0.17.zip</download-url>
      <description>
        
          &lt;h1 id="wecode-plugin-for-intellij-platform"&gt;Wecode Plugin for IntelliJ Platform&lt;/h1&gt;
          &lt;p&gt;Wecode is an AI coding assistant that can suggest multi-line code or full functions in real-time.&lt;/p&gt;
          &lt;br/&gt;
          &lt;h2 id="requirements"&gt;Requirements&lt;/h2&gt;
          Wecode plugin requires &lt;a href="https://nodejs.org/"&gt;Node.js&lt;/a&gt; v18+ installed.
        
      </description>
      <change-notes>
        
          &lt;a href="https://wiki.api.weibo.com/zh/weibo_rd/dev/wecode/plugin/idea.md"&gt;Wecode Plugin ChangeLog&lt;/a&gt;
        
      </change-notes>
    </idea-plugin>
    <idea-plugin size="26235887" date="1725031283000" updatedDate="1725031283000" url="http://wecode.api.weibo.com/idea/download/updatePlugins.xml">
      <name>Wecode</name>
      <id>com.weibo.assistant</id>
      <version>3.0.2</version>
      <vendor url="https://weibo.com">Wecode</vendor>
      <idea-version since-build="213" until-build="231.*"/>
      <download-url>http://wecode.api.weibo.com/idea/download/plugins/com.weibo.assistant/CodeAssistant/3.0.2/CodeAssistant-3.0.2.zip</download-url>
      <description>
        
          &lt;h1&gt;Wecode Plugin (Legacy)&lt;/h1&gt;
          &lt;p&gt;This version supports IntelliJ IDEA 2021.3 to 2023.1.&lt;/p&gt;
        
      </description>
      <change-notes>
        
          Legacy version for older IntelliJ builds.
        
      </change-notes>
    </idea-plugin>
  </category>
  <category name="Development Tools">
    <idea-plugin>
      <name>Wecoder</name>
      <id>com.sina.wecoder</id>
      <version>1.0.1</version>
      <vendor url="https://weibo.com">Wecoder</vendor>
      <idea-version since-build="232" until-build=""/>
      <download-url>http://localhost:9000/wecodeplugin/com.sina.wecoder/1.0.1/plugin.zip</download-url>
      <description>Wecoder</description>
      <change-notes>1</change-notes>
    </idea-plugin>
    <idea-plugin>
      <name>Wecode</name>
      <id>com.weibo.assistant</id>
      <version>3.0.18</version>
      <vendor url="https://weibo.com">Wecode</vendor>
      <idea-version since-build="232" until-build=""/>
      <download-url>http://localhost:9000/wecodeplugin/com.weibo.assistant/3.0.18/plugin.jar</download-url>
      <description>&#xD;
        &#xD;
          &lt;h1 id="wecode-plugin-for-intellij-platform"&gt;Wecode Plugin for IntelliJ Platform&lt;/h1&gt;&#xD;
          &lt;p&gt;Wecode is an AI coding assistant that can suggest multi-line code or full functions in real-time.&lt;/p&gt;&#xD;
          &lt;br/&gt;&#xD;
          &lt;h2 id="requirements"&gt;Requirements&lt;/h2&gt;&#xD;
          Wecode plugin requires &lt;a href="https://nodejs.org/"&gt;Node.js&lt;/a&gt; v18+ installed.&#xD;
        &#xD;
      </description>
      <change-notes>更新新版本</change-notes>
    </idea-plugin>
  </category>
</plugin-repository>