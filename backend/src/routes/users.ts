import express, { Router } from 'express';
import { UserService } from '../services/user.service';
import { authenticateToken, requireAdmin, generateToken } from '../middleware/auth';
import { CreateUserDto, LoginDto, CreatePermissionDto } from '../types/user';

export const router: Router = express.Router();
const userService = UserService.getInstance();

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body as LoginDto;
    const user = await userService.validateUser(username, password);
    const token = generateToken(user);
    res.json({ token, user: { ...user, password: undefined } });
  } catch (error) {
    res.status(401).json({ message: (error as Error).message });
  }
});

// 获取用户列表（仅管理员）
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await userService.getUsers();
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
});

// 创建新用户（仅管理员）
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const createUserDto = req.body as CreateUserDto;
    const newUser = await userService.createUser(createUserDto);
    res.status(201).json({ ...newUser, password: undefined });
  } catch (error) {
    res.status(400).json({ message: (error as Error).message });
  }
});

// 删除用户（仅管理员）
router.delete('/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    await userService.deleteUser(req.params.userId);
    res.status(204).send();
  } catch (error) {
    res.status(404).json({ message: (error as Error).message });
  }
});

// 获取权限列表（仅管理员）
router.get('/permissions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const permissions = await userService.getPermissions();
    res.json(permissions);
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
});

// 创建新权限（仅管理员）
router.post('/permissions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const createPermissionDto = req.body as CreatePermissionDto;
    const newPermission = await userService.createPermission(createPermissionDto);
    res.status(201).json(newPermission);
  } catch (error) {
    res.status(400).json({ message: (error as Error).message });
  }
});

// 删除权限（仅管理员）
router.delete('/permissions/:permissionId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    await userService.deletePermission(req.params.permissionId);
    res.status(204).send();
  } catch (error) {
    res.status(404).json({ message: (error as Error).message });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    // 从数据库中获取完整的用户信息
    const users = await userService.getUsers();
    const currentUser = users.find(user => user.id === req.user?.id);
    
    if (!currentUser) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    res.json({ ...currentUser, password: undefined });
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
});

// 更新用户权限（仅管理员）
router.put('/:userId/permissions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { permissions } = req.body;
    const updatedUser = await userService.updateUserPermissions(req.params.userId, permissions);
    res.json(updatedUser);
  } catch (error) {
    res.status(400).json({ message: (error as Error).message });
  }
});