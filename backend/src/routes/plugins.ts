import express, { Router } from 'express';

import multer from 'multer';
import { XmlManager, PluginRepositoryWrapper } from '../utils/xml-manager';
import { PluginParser } from '../utils/plugin-parser';
import { MinioClient } from '../utils/minio-client';
import { VersionManager } from '../utils/version-manager';
import { UploadPluginDto, PluginInfo } from '../types/plugin';
import { parseStringPromise } from 'xml2js';

export const router: Router = express.Router();

// 配置 multer 用于处理文件上传
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 1024 * 1024 * 1024 // 限制文件大小为 1GB
  }
});

// 获取插件列表
router.get('/', async (req, res) => {
  try {
    const xmlManager = XmlManager.getInstance();
    const repository = await xmlManager.readPluginRepository();
    res.json(repository);
  } catch (error) {
    console.error('Error getting plugins list:', error);
    res.status(500).json({ error: 'Failed to get plugins list' });
  }
});

// 创建新插件
router.post('/', async (req, res) => {
  try {
    const { id, name, category, vendor, vendorUrl, version, sinceBuild, untilBuild, description } = req.body;

    // 验证必填字段
    if (!id || !name || !category || !vendor || !version || !sinceBuild) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // 检查插件是否已存在
    const xmlManager = XmlManager.getInstance();
    const repository = await xmlManager.readPluginRepository();
    const existingPlugin = repository['plugin-repository'].category
      .flatMap(cat => cat['idea-plugin'] || [])
      .find(p => p.id[0] === id);

    if (existingPlugin) {
      return res.status(409).json({ error: 'Plugin ID already exists' });
    }

    // 创建新插件
    const pluginInfo: PluginInfo = {
      id: [id],
      name: [name],
      version: [version],
      vendor: [{ _: vendor, $: { url: vendorUrl } }],
      'idea-version': [{
        $: {
          'since-build': sinceBuild,
          'until-build': untilBuild || ''
        }
      }],
      description: [description || ''],
      'download-url': [''],
      'change-notes': ['']
    };

    await xmlManager.addPlugin(category, pluginInfo);

    res.status(201).json({
      status: 'success',
      data: pluginInfo
    });
  } catch (error) {
    console.error('Error creating plugin:', error);
    res.status(500).json({ error: 'Failed to create plugin' });
  }
});

// 上传新插件
router.post('/:pluginId/versions', upload.single('plugin'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No plugin file uploaded' });
    }

    const pluginId = req.params.pluginId;
    const pluginFile = req.file;
    const uploadData = req.body as UploadPluginDto;
    
    // 解析插件文件
    const pluginParser = new PluginParser(pluginFile.buffer);
    const pluginInfo = await pluginParser.parsePluginInfo({
      id: pluginId,
      name: uploadData.name,
      category: uploadData.category,
      vendor: uploadData.vendor,
      vendorUrl: uploadData.vendorUrl,
      version: uploadData.version,
      sinceBuild: uploadData.sinceBuild,
      untilBuild: uploadData.untilBuild,
      description: uploadData.description,
      changeNotes: uploadData.changeNotes
    });

    // 上传到 MinIO
    const minioClient = MinioClient.getInstance();
    const downloadUrl = await minioClient.uploadPlugin(
      pluginId,
      uploadData.version,
      pluginFile.buffer
    );

    // 检查插件是否已存在
    const xmlManager = XmlManager.getInstance();
    const existingPlugin = await xmlManager.getPlugin(pluginId);

    if (!existingPlugin) {
      return res.status(404).json({ error: 'Plugin not found. Please create the plugin first.' });
    }

    // 如果版本已存在，先删除旧版本
    const existingVersion = existingPlugin.version.includes(uploadData.version);
    if (existingVersion) {
      console.log(`Version ${uploadData.version} already exists, removing old version...`);
      await xmlManager.removePlugin(pluginId, uploadData.version);
    }

    // 添加新版本
    await xmlManager.addPlugin(uploadData.category, {
      ...pluginInfo,
      'download-url': [downloadUrl]
    });

    res.json({
      status: 'success',
      data: {
        pluginId,
        version: uploadData.version,
        downloadUrl
      }
    });
  } catch (error) {
    console.error('Error uploading plugin:', error);
    res.status(500).json({ error: 'Failed to upload plugin' });
  }
});

// 删除插件版本
router.delete('/:pluginId/versions/:version', async (req, res) => {
  try {
    const { pluginId, version } = req.params;

    // 从 MinIO 删除文件
    const minioClient = MinioClient.getInstance();
    await minioClient.deletePlugin(pluginId, version);

    // 从 XML 中删除记录
    const xmlManager = XmlManager.getInstance();
    await xmlManager.removePlugin(pluginId, version);

    res.json({ status: 'success' });
  } catch (error) {
    console.error('Error deleting plugin:', error);
    res.status(500).json({ error: 'Failed to delete plugin' });
  }
});

// 获取版本历史列表
router.get('/versions', async (req, res) => {
  try {
    const versionManager = VersionManager.getInstance();
    const versions = await versionManager.listVersions();
    res.json({
      versions: versions.map(version => {
        const parts = version.split('_');
        let timestamp = '';
        if (parts.length > 1) {
          const timeStr = parts[1].replace('.xml', '').replace(/-/g, ':');
          const date = new Date(timeStr);
          timestamp = !isNaN(date.getTime()) ? timeStr : '';
        }
        return {
          fileName: version,
          timestamp
        };
      })
    });
  } catch (error) {
    console.error('Error getting version history:', error);
    res.status(500).json({ error: 'Failed to get version history' });
  }
});

// 回滚到指定版本
router.post('/versions/rollback', async (req, res) => {
  try {
    const { version } = req.body;
    if (!version) {
      return res.status(400).json({ error: 'Version file name is required' });
    }

    const versionManager = VersionManager.getInstance();
    const xmlManager = XmlManager.getInstance();
    
    // 获取指定版本的内容
    const xmlContent = await versionManager.rollbackToVersion(version);
    
    // 解析 XML 内容
    const repository = await parseStringPromise(xmlContent) as PluginRepositoryWrapper;
    
    // 写入当前 XML 文件
    await xmlManager.writePluginRepository(repository);

    res.json({ status: 'success', message: `Rolled back to version ${version}` });
  } catch (error) {
    console.error('Error rolling back version:', error);
    res.status(500).json({ error: 'Failed to roll back version' });
  }
});

// 获取插件详情
router.get('/:pluginId', async (req, res) => {
  try {
    const pluginId = req.params.pluginId;
    const xmlManager = XmlManager.getInstance();
    const plugin = await xmlManager.getPlugin(pluginId);

    if (!plugin) {
      return res.status(404).json({ error: 'Plugin not found' });
    }

    res.json(plugin);
  } catch (error) {
    console.error('Error getting plugin details:', error);
    res.status(500).json({ error: 'Failed to get plugin details' });
  }
});