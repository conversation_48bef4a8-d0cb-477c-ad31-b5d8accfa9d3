import { Client } from 'minio';
import { minioConfig } from '../config/minio.config';

let minioClient: Client;

export const initializeMinioClient = async () => {
  minioClient = new Client({
    endPoint: minioConfig.endpoint,
    port: minioConfig.port,
    useSSL: minioConfig.useSSL,
    accessKey: minioConfig.accessKey,
    secretKey: minioConfig.secretKey
  });

  console.log('Minio client initialized. minioConfig: ', minioConfig);
  const bucketName = minioConfig.bucket;

  // 确保 bucket 存在
  const bucketExists = await minioClient.bucketExists(bucketName);
  if (!bucketExists) {
    await minioClient.makeBucket(bucketName);
    console.log(`Bucket '${bucketName}' created successfully`);
  }

  return minioClient;
};

export const uploadPlugin = async (pluginId: string, version: string, fileBuffer: Buffer, filename: string) => {
  const bucketName = minioConfig.bucket;
  const objectName = `${pluginId}/${version}/${filename}`;

  await minioClient.putObject(bucketName, objectName, fileBuffer);
  
  // 生成预签名下载URL，有效期24小时
  const downloadUrl = await minioClient.presignedGetObject(bucketName, objectName, 24 * 60 * 60);
  
  return {
    objectName,
    downloadUrl
  };
};

export const deletePlugin = async (pluginId: string, version: string, filename: string) => {
  const bucketName = minioConfig.bucket;
  const objectName = `${pluginId}/${version}/${filename}`;

  await minioClient.removeObject(bucketName, objectName);
};

export const getPluginDownloadUrl = async (pluginId: string, version: string, filename: string) => {
  const bucketName = minioConfig.bucket;
  const objectName = `${pluginId}/${version}/${filename}`;

  return await minioClient.presignedGetObject(bucketName, objectName, 24 * 60 * 60);
};