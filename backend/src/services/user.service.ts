import { User, UserRole, CreateUserDto, Permission, CreatePermissionDto } from '../types/user';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// 模拟数据库存储
let users: User[] = [];
let permissions: Permission[] = [];

export class UserService {
  private static instance: UserService;

  private constructor() {}

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
      // 确保每次启动都创建默认管理员账户
      UserService.instance.ensureDefaultAdmin();
    }
    return UserService.instance;
  }

  private async ensureDefaultAdmin(): Promise<void> {
    // 检查是否已存在管理员用户
    const adminExists = users.some(u => u.username === 'admin');
    if (!adminExists) {
      try {
        await this.createUser({
          username: 'admin',
          password: 'admin123',
          role: UserRole.ADMIN
        });
        console.log('默认管理员账户已创建: admin/admin123');
      } catch (error) {
        console.log('默认管理员账户已存在');
      }
    }
  }

  async createUser(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = users.find(u => u.username === createUserDto.username);
    if (existingUser) {
      throw new Error('用户名已存在');
    }

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    const newUser: User = {
      id: uuidv4(),
      username: createUserDto.username,
      password: hashedPassword,
      role: createUserDto.role || UserRole.USER,
      permissions: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    users.push(newUser);
    return newUser;
  }

  async validateUser(username: string, password: string): Promise<User> {
    const user = users.find(u => u.username === username);
    if (!user) {
      throw new Error('用户不存在');
    }

    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      throw new Error('密码错误');
    }

    return user;
  }

  async getUsers(): Promise<User[]> {
    return users.map(({ password, ...user }) => user as User);
  }

  async createPermission(createPermissionDto: CreatePermissionDto): Promise<Permission> {
    const newPermission: Permission = {
      id: uuidv4(),
      ...createPermissionDto,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    permissions.push(newPermission);
    return newPermission;
  }

  async getPermissions(): Promise<Permission[]> {
    return permissions;
  }

  async deleteUser(userId: string): Promise<void> {
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }
    users.splice(userIndex, 1);
  }

  async deletePermission(permissionId: string): Promise<void> {
    const permissionIndex = permissions.findIndex(p => p.id === permissionId);
    if (permissionIndex === -1) {
      throw new Error('权限不存在');
    }
    permissions.splice(permissionIndex, 1);

    // 从所有用户中移除被删除的权限
    users = users.map(user => ({
      ...user,
      permissions: user.permissions?.filter(p => p !== permissionId) || []
    }));
  }

  async updateUserPermissions(userId: string, permissionIds: string[]): Promise<Omit<User, 'password'>> {
    const user = users.find(u => u.id === userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    // 验证所有权限ID是否存在
    const invalidPermissions = permissionIds.filter(
      id => !permissions.find(p => p.id === id)
    );
    if (invalidPermissions.length > 0) {
      throw new Error('存在无效的权限ID');
    }

    user.permissions = permissionIds;
    user.updatedAt = new Date();

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
}