import { config } from 'dotenv';

// 加载测试环境变量
config({ path: '.env.test' });

// 设置测试环境变量默认值
process.env.PORT = process.env.PORT || '3000';
process.env.MINIO_ENDPOINT = process.env.MINIO_ENDPOINT || 'localhost';
process.env.MINIO_PORT = process.env.MINIO_PORT || '9000';
process.env.MINIO_USE_SSL = process.env.MINIO_USE_SSL || 'false';
process.env.MINIO_ACCESS_KEY = process.env.MINIO_ACCESS_KEY || 'test_access_key';
process.env.MINIO_SECRET_KEY = process.env.MINIO_SECRET_KEY || 'test_secret_key';
process.env.MINIO_BUCKET = process.env.MINIO_BUCKET || 'test-jetbrains-plugins';

// Mock MinIO 客户端
jest.mock('../services/minio', () => ({
  initializeMinioClient: jest.fn().mockResolvedValue(undefined),
  uploadPlugin: jest.fn().mockImplementation((pluginId, version, fileBuffer, filename) => {
    return Promise.resolve({
      objectName: `${pluginId}/${version}/${filename}`,
      downloadUrl: `http://localhost:9000/test-jetbrains-plugins/${pluginId}/${version}/${filename}`
    });
  }),
  deletePlugin: jest.fn().mockResolvedValue(undefined),
  getPluginDownloadUrl: jest.fn().mockImplementation((pluginId, version, filename) => {
    return Promise.resolve(
      `http://*************:9000/test-jetbrains-plugins/${pluginId}/${version}/${filename}`
    );
  })
}));

// 创建测试用的临时文件
import fs from 'fs/promises';
import path from 'path';

beforeAll(async () => {
  // 创建测试用的 updatePlugins.xml
  const testXml = `<?xml version="1.0" encoding="UTF-8"?>
<plugin-repository>
  <category name="Test">
  </category>
</plugin-repository>`;

  try {
    await fs.writeFile(
      path.join(process.cwd(), 'updatePlugins.xml'),
      testXml
    );
  } catch (error) {
    console.error('Error creating test files:', error);
  }
});

// 清理测试文件
afterAll(async () => {
  try {
    await fs.unlink(path.join(process.cwd(), 'updatePlugins.xml'));
  } catch (error) {
    console.error('Error cleaning up test files:', error);
  }
});