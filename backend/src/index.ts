import express from 'express';
import { router as pluginRouter } from './routes/plugins';
import { router as userRouter } from './routes/users';
import { initializeMinioClient } from './services/minio';
import { errorHandler, notFoundHandler } from './middleware/error-handler';
import { authenticateToken } from './middleware/auth';
import path from 'path';

// 加载环境变量
import dotenv from 'dotenv';
dotenv.config();

const app = express();
const port = process.env.PORT || 3000;

// 中间件配置
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(process.cwd(), 'public')));

// CORS 配置
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// 用户相关路由
app.use('/api/users', userRouter);

// 需要认证的路由
app.use('/api/plugins', authenticateToken, pluginRouter);

// 插件仓库 XML 端点
app.get('/plugins.xml', (req, res) => {
  res.header('Content-Type', 'application/xml');
  res.sendFile(path.join(process.cwd(), 'updatePlugins.xml'));
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// SPA 路由支持 - 将所有非 API 和非静态文件的请求都返回 index.html
app.get('*', (req, res, next) => {
  // 跳过 API 路由和静态文件
  if (req.path.startsWith('/api/') || req.path.startsWith('/plugins.xml') || req.path.startsWith('/health')) {
    return next();
  }
  
  // 对于其他所有请求，返回 index.html
  res.sendFile(path.join(process.cwd(), 'public', 'index.html'));
});

// 404 处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 初始化 MinIO 客户端
initializeMinioClient().then(() => {
  console.log('MinIO client initialized');
}).catch(error => {
  console.error('Failed to initialize MinIO client:', error);
});

// 启动服务器
app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});