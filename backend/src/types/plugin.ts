export interface IdeaVersion {
  $: {
    'since-build': string;
    'until-build'?: string;
  };
}

export interface Vendor {
  _: string;
  $: {
    url: string;
  };
}

export interface PluginInfo {
  $?: {
    size?: string;
    date?: string;
    updatedDate?: string;
    url?: string;
  };
  name: string[];
  id: string[];
  version: string[];
  vendor: Vendor[];
  'idea-version': IdeaVersion[];
  'download-url': string[];
  description: string[];
  'change-notes': string[];
}

export interface Category {
  $: {
    name: string;
  };
  'idea-plugin': PluginInfo[];
}

export interface PluginRepository {
  category: Category[];
}

export interface UploadPluginDto {
  name: string;
  category: string;
  vendor: string;
  vendorUrl: string;
  version: string;
  sinceBuild: string;
  untilBuild?: string;
  description: string;
  changeNotes: string;
  size?: string;
  date?: string;
  updatedDate?: string;
  url?: string;
}

export interface PluginUploadResult {
  pluginId: string;
  version: string;
  downloadUrl: string;
}

export interface ErrorResponse {
  error: string;
}