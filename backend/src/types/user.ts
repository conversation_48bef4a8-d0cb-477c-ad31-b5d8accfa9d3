export interface User {
  id: string;
  username: string;
  password: string;
  role: UserRole;
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  path: string;
  method: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDto {
  username: string;
  password: string;
  role?: UserRole;
}

export interface LoginDto {
  username: string;
  password: string;
}

export interface CreatePermissionDto {
  name: string;
  description: string;
  path: string;
  method: string;
}