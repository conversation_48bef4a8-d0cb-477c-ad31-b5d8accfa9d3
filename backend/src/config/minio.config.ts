interface MinioConfig {
  endpoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  bucket: string;
  region: string;
  publicUrl: string;
}

export const minioConfig: MinioConfig = {
  endpoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
  bucket: process.env.MINIO_BUCKET || 'wecodeplugin',
  region: process.env.MINIO_REGION || 'us-east-1',
  publicUrl: process.env.MINIO_PUBLIC_URL || `http://localhost:${process.env.MINIO_PORT || '9000'}`
};