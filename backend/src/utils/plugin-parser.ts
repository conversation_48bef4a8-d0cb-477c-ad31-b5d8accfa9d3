import { parseStringPromise } from 'xml2js';
import AdmZ<PERSON> from 'adm-zip';
import { PluginInfo } from '../types/plugin';

interface ParsePluginInfoParams {
  id: string;
  name: string;
  category: string;
  vendor: string;
  vendorUrl: string;
  version: string;
  sinceBuild: string;
  untilBuild?: string;
  description: string;
  changeNotes: string;
}

export class PluginParser {
  private buffer: Buffer;
  private zip: AdmZip;

  constructor(buffer: Buffer) {
    this.buffer = buffer;
    this.zip = new AdmZip(buffer);
  }

  public async parsePluginXml(): Promise<{
    version: string;
    sinceBuild: string;
    untilBuild?: string;
    name: string;
    description: string;
  }> {
    const entries = this.zip.getEntries();
    const pluginXmlEntry = entries.find(entry => entry.entryName.toLowerCase() === 'meta-inf/plugin.xml');
    
    if (!pluginXmlEntry) {
      return {
        version: '',
        sinceBuild: '',
        untilBuild: '',
        name: '',
        description: ''
      };
    }

    const xmlContent = pluginXmlEntry.getData().toString('utf8');
    const result = await parseStringPromise(xmlContent);
    const ideaPlugin = result.idea?.plugin || {};

    return {
      version: ideaPlugin.version?.[0] || '',
      sinceBuild: ideaPlugin['idea-version']?.[0]?.$?.['since-build'] || '',
      untilBuild: ideaPlugin['idea-version']?.[0]?.$?.['until-build'] || '',
      name: ideaPlugin.name?.[0] || '',
      description: ideaPlugin.description?.[0] || ''
    };
  }

  public async parsePluginInfo(params: ParsePluginInfoParams): Promise<PluginInfo> {
    const pluginXml = await this.parsePluginXml();
    
    return {
      name: [params.name],
      id: [params.id],
      version: [params.version],
      vendor: [{ _: params.vendor, $: { url: params.vendorUrl } }],
      'idea-version': [{
        $: {
          'since-build': params.sinceBuild,
          'until-build': params.untilBuild || ''
        }
      }],
      'download-url': [], // 将由上传后的 URL 填充
      description: [params.description || pluginXml.description],
      'change-notes': [params.changeNotes || '']
    };
  }

  public validatePluginStructure(): void {
    // 检查文件大小
    const totalSize = this.zip.getEntries().reduce((size, entry) => {
      return size + entry.getData().length;
    }, 0);

    const maxSize = 1024 * 1024 * 1024; // 1GB
    if (totalSize > maxSize) {
      throw new Error(`Plugin size exceeds maximum limit of ${maxSize} bytes`);
    }

    // 检查文件类型
    const invalidFiles = this.zip.getEntries().filter(entry => {
      const ext = entry.name.toLowerCase().split('.').pop();
      return ext && ![
        'xml', 'jar', 'zip', 'class', 'java', 'kt', 'properties',
        'txt', 'md', 'html', 'css', 'js', 'json', 'png', 'jpg',
        'gif', 'svg', 'ico', 'ttf', 'woff', 'woff2', 'eot'
      ].includes(ext);
    });

    if (invalidFiles.length > 0) {
      throw new Error(`Invalid file types found: ${invalidFiles.map(f => f.name).join(', ')}`);
    }
  }
}