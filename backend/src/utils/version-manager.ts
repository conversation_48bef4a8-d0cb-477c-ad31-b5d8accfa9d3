import { PluginRepositoryWrapper } from './xml-manager';
import { MinioClient } from './minio-client';

export class VersionManager {
  private static instance: VersionManager;
  private maxHistoryCount: number;
  private minioClient: MinioClient;

  private constructor(maxHistoryCount: number = 10) {
    this.maxHistoryCount = maxHistoryCount;
    this.minioClient = MinioClient.getInstance();
  }

  public static getInstance(maxHistoryCount?: number): VersionManager {
    if (!VersionManager.instance) {
      VersionManager.instance = new VersionManager(maxHistoryCount);
    }
    return VersionManager.instance;
  }



  private generateVersionTimestamp(): string {
    return new Date().toISOString().replace(/[:.]/g, '-');
  }

  public async saveVersion(xmlContent: string): Promise<string> {
    const timestamp = this.generateVersionTimestamp();
    await this.minioClient.uploadXmlVersion(timestamp, Buffer.from(xmlContent));
    await this.cleanupOldVersions();
    return timestamp;
  }

  public async saveCurrent(xmlContent: string): Promise<void> {
    await this.minioClient.uploadXmlCurrent(Buffer.from(xmlContent));
    await this.cleanupOldVersions();
  }

  private async cleanupOldVersions(): Promise<void> {
    const versions = await this.minioClient.listXmlVersions();
    
    if (versions.length > this.maxHistoryCount) {
      const versionsToDelete = versions.slice(versions.length - this.maxHistoryCount);
      
      for (const version of versionsToDelete) {
        await this.minioClient.deleteXmlVersion(version);
      }
    }
  }

  public async listVersions(): Promise<string[]> {
    return await this.minioClient.listXmlVersions();
  }

  public async rollbackToVersion(timestamp: string): Promise<string> {
    try {
      const buffer = await this.minioClient.getXmlVersion(timestamp);
      return buffer.toString('utf-8');
    } catch (error) {
      throw new Error(`Version not found: ${timestamp}`);
    }
  }

  public async getCurrentVersion(): Promise<string> {
    try {
      const buffer = await this.minioClient.getXmlCurrent();
      return buffer.toString('utf-8');
    } catch (error) {
      throw new Error(`Current not found.`);
    }
  }
}