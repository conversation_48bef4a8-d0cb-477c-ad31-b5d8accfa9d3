import fs from 'fs/promises';
import { parseStringPromise, Builder } from 'xml2js';
import { PluginRepository, PluginInfo, Category } from '../types/plugin';
import { VersionManager } from './version-manager';

export interface PluginRepositoryWrapper {
  'plugin-repository': PluginRepository;
}

export class XmlManager {
  private static instance: XmlManager;
  private xmlPath: string;
  private builder: Builder;

  private constructor(xmlPath: string) {
    this.xmlPath = xmlPath;
    this.builder = new Builder({
      renderOpts: { pretty: true, indent: '  ' },
      xmldec: { version: '1.0', encoding: 'UTF-8' },
      cdata: true
    });
  }

  public static getInstance(xmlPath: string = 'updatePlugins.xml'): XmlManager {
    if (!XmlManager.instance) {
      XmlManager.instance = new XmlManager(xmlPath);
    }
    return XmlManager.instance;
  }

  public async readPluginRepository(): Promise<PluginRepositoryWrapper> {
    try {
      // 获取最新版本列表
      const versionManager = VersionManager.getInstance();
      const xmlContent = await versionManager.getCurrentVersion();
      return await parseStringPromise(xmlContent) as PluginRepositoryWrapper;
    } catch (error) {
      console.warn('Failed to read from MinIO, creating new repository:', error);
    }

    // 如果没有找到版本或出错，创建新的空仓库
    return {
      'plugin-repository': {
        category: []
      }
    };
  }

  public async writePluginRepository(repository: PluginRepositoryWrapper): Promise<void> {
    const xml = this.builder.buildObject(repository);
    await fs.writeFile(this.xmlPath, xml, 'utf-8');
    
    // 保存版本历史
    const versionManager = VersionManager.getInstance();
    await versionManager.saveCurrent(xml);
  }

  public async addPlugin(categoryName: string, plugin: PluginInfo): Promise<void> {
    const repository = await this.readPluginRepository();
    
    let category = repository['plugin-repository'].category.find(
      (cat: Category) => cat.$.name === categoryName
    );

    if (!category) {
      category = {
        $: { name: categoryName },
        'idea-plugin': []
      };
      repository['plugin-repository'].category.push(category);
    }

    // 确保 idea-plugin 数组存在
    if (!category['idea-plugin']) {
      category['idea-plugin'] = [];
    }

    // 处理插件属性
    const processedPlugin = {
      ...plugin,
      $: {
        size: plugin.$?.size,
        date: plugin.$?.date || new Date().getTime().toString(),
        updatedDate: plugin.$?.updatedDate || new Date().getTime().toString(),
        url: plugin.$?.url || "http://wecode.api.weibo.com/idea/download/updatePlugins.xml"
      },
      description: [plugin.description[0].replace(/<!\[CDATA\[([\s\S]*?)\]\]>/g, '$1').trim()],
      'change-notes': [plugin['change-notes'][0].replace(/<!\[CDATA\[([\s\S]*?)\]\]>/g, '$1').trim()]
    };

    category['idea-plugin'].push(processedPlugin);
    await this.writePluginRepository(repository);
  }

  public async removePlugin(pluginId: string, version: string): Promise<void> {
    const repository = await this.readPluginRepository();
    let found = false;

    repository['plugin-repository'].category.forEach((category: Category) => {
      if (category['idea-plugin']) {
        const index = category['idea-plugin'].findIndex(
          (plugin: PluginInfo) => plugin.id[0] === pluginId && plugin.version[0] === version
        );

        if (index !== -1) {
          category['idea-plugin'].splice(index, 1);
          found = true;
        }
      }
    });

    if (!found) {
      throw new Error('Plugin version not found');
    }

    await this.writePluginRepository(repository);
  }

  public async getPlugin(pluginId: string): Promise<PluginInfo | null> {
    const repository = await this.readPluginRepository();
    
    for (const category of repository['plugin-repository'].category) {
      if (category['idea-plugin']) {
        const plugin = category['idea-plugin'].find(
          (p: PluginInfo) => p.id[0] === pluginId
        );
        if (plugin) {
          return plugin;
        }
      }
    }

    return null;
  }

  public async updatePluginDownloadUrl(pluginId: string, version: string, downloadUrl: string): Promise<void> {
    const repository = await this.readPluginRepository();
    let found = false;

    repository['plugin-repository'].category.forEach((category: Category) => {
      if (category['idea-plugin']) {
        const plugin = category['idea-plugin'].find(
          (p: PluginInfo) => p.id[0] === pluginId && p.version[0] === version
        );

        if (plugin) {
          plugin['download-url'] = [downloadUrl];
          found = true;
        }
      }
    });

    if (!found) {
      throw new Error('Plugin version not found');
    }

    await this.writePluginRepository(repository);
  }
}