import { Client } from 'minio';
import { minioConfig } from '../config/minio.config';

export class MinioClient {
  private static instance: MinioClient;
  private client: Client;
  private bucketName: string;

  private constructor() {
    this.client = new Client({
      endPoint: minioConfig.endpoint,
      port: minioConfig.port,
      useSSL: minioConfig.useSSL,
      accessKey: minioConfig.accessKey,
      secretKey: minioConfig.secretKey
    });
    this.bucketName = minioConfig.bucket;
  }

  public static getInstance(): MinioClient {
    if (!MinioClient.instance) {
      MinioClient.instance = new MinioClient();
    }
    return MinioClient.instance;
  }

  private async ensureBucketExists(): Promise<void> {
    const exists = await this.client.bucketExists(this.bucketName);
    if (!exists) {
      await this.client.makeBucket(this.bucketName, minioConfig.region);
    }
  }

  public async uploadPlugin(pluginId: string, version: string, buffer: Buffer): Promise<string> {
    await this.ensureBucketExists();

    const objectName = `${pluginId}/${version}/plugin.zip`;
    await this.client.putObject(this.bucketName, objectName, buffer);

    // 生成下载 URL
    const baseUrl = minioConfig.publicUrl;
    return `${baseUrl}/${this.bucketName}/${objectName}`;
  }

  public async deletePlugin(pluginId: string, version: string): Promise<void> {
    const objectName = `${pluginId}/${version}/plugin.zip`;
    await this.client.removeObject(this.bucketName, objectName);
  }

  public async getPluginUrl(pluginId: string, version: string): Promise<string> {
    const objectName = `${pluginId}/${version}/plugin.zip`;
    const baseUrl = minioConfig.publicUrl;
    return `${baseUrl}/${this.bucketName}/${objectName}`;
  }

  public async uploadXmlVersion(timestamp: string, buffer: Buffer): Promise<string> {
    await this.ensureBucketExists();

    const objectName = `xml_versions/${timestamp}.xml`;
    await this.client.putObject(this.bucketName, objectName, buffer);

    const baseUrl = minioConfig.publicUrl;
    return `${baseUrl}/${this.bucketName}/${objectName}`;
  }

  public async uploadXmlCurrent(buffer: Buffer): Promise<string> {
    await this.ensureBucketExists();

    const objectName = `updatePlugins.xml`;
    await this.client.putObject(this.bucketName, objectName, buffer);

    const baseUrl = minioConfig.publicUrl;
    return `${baseUrl}/${this.bucketName}/${objectName}`;
  }

  public async getXmlVersion(timestamp: string): Promise<Buffer> {
    const objectName = `xml_versions/${timestamp}.xml`;
    return new Promise((resolve, reject) => {
      this.client.getObject(this.bucketName, objectName)
        .then(dataStream => {
          const chunks: Buffer[] = [];
          dataStream.on('data', (chunk: Buffer) => chunks.push(chunk));
          dataStream.on('end', () => resolve(Buffer.concat(chunks)));
          dataStream.on('error', (err: Error) => reject(err));
        })
        .catch(err => reject(err));
    });
  }

  public async getXmlCurrent(): Promise<Buffer> {
    const objectName = `updatePlugins.xml`;
    return new Promise((resolve, reject) => {
      this.client.getObject(this.bucketName, objectName)
        .then(dataStream => {
          const chunks: Buffer[] = [];
          dataStream.on('data', (chunk: Buffer) => chunks.push(chunk));
          dataStream.on('end', () => resolve(Buffer.concat(chunks)));
          dataStream.on('error', (err: Error) => reject(err));
        })
        .catch(err => reject(err));
    });
  }

  public async listXmlVersions(): Promise<string[]> {
    const prefix = 'xml_versions/';
    const versions: string[] = [];

    const stream = this.client.listObjects(this.bucketName, prefix, true);
    
    return new Promise((resolve, reject) => {
      stream.on('data', (obj) => {
        if (obj.name) {
          const timestamp = obj.name.replace(prefix, '').replace('.xml', '');
          versions.push(timestamp);
        }
      });

      stream.on('end', () => resolve(versions.sort().reverse()));
      stream.on('error', (err) => reject(err));
    });
  }

  public async deleteXmlVersion(timestamp: string): Promise<void> {
    const objectName = `xml_versions/${timestamp}.xml`;
    await this.client.removeObject(this.bucketName, objectName);
  }
}